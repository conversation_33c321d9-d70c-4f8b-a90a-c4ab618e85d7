{"$schema": "https://github.com/fastfetch-cli/fastfetch/raw/dev/doc/json_schema.json", "logo": {"type": "builtin", "source": "fedora", "color": "red", "padding": {"top": 2, "left": 2, "right": 2}}, "display": {"separator": " ║ ", "color": "white"}, "modules": ["title", {"type": "os", "key": "OS", "keyIcon": "", "keyColor": "cyan"}, {"type": "kernel", "key": "<PERSON><PERSON>", "keyIcon": "", "keyColor": "cyan"}, {"type": "cpu", "key": "CPU", "keyIcon": "", "format": "{name} ({cores-physical}C/{cores-logical}T) @ {freq-max}"}, {"type": "gpu", "key": "GPU", "keyIcon": "", "format": "{name} ({driver})"}, {"type": "memory", "key": "Memory", "keyIcon": "", "format": "{used:.2f} GiB / {total:.2f} GiB"}, {"type": "disk", "key": "Disk", "keyIcon": "", "format": "{used:.2f} GiB / {total:.2f} GiB"}, {"type": "uptime", "key": "Uptime", "keyIcon": ""}, {"type": "wm", "key": "WM", "keyIcon": "", "format": "{name}"}, {"type": "colors", "key": "Colors", "keyIcon": ""}, {"type": "custom", "key": "Packages", "keyIcon": "", "command": "dnf list installed | wc -l"}]}